.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 750rpx;
  height: 1748rpx;
  overflow: hidden;
  .block_1 {
    width: 750rpx;
    height: 194rpx;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 64rpx;
      .text_1 {
        width: 64rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 24rpx;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin: 14rpx 0 0 32rpx;
      }
      .thumbnail_1 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 502rpx;
      }
      .thumbnail_2 {
        width: 36rpx;
        height: 36rpx;
        margin: 14rpx 0 0 6rpx;
      }
      .thumbnail_3 {
        width: 38rpx;
        height: 38rpx;
        margin: 14rpx 30rpx 0 6rpx;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 108rpx;
      margin-bottom: 22rpx;
      .thumbnail_4 {
        width: 18rpx;
        height: 34rpx;
        margin: 38rpx 0 0 36rpx;
      }
      .text_2 {
        width: 128rpx;
        height: 44rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 32rpx;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 44rpx;
        margin: 32rpx 0 0 22rpx;
      }
      .image_1 {
        width: 174rpx;
        height: 64rpx;
        margin: 22rpx 12rpx 0 360rpx;
      }
    }
  }
  .block_2 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 522rpx;
    width: 702rpx;
    position: relative;
    margin: -2rpx 0 0 24rpx;
    .text-wrapper_1 {
      width: 656rpx;
      height: 26rpx;
      margin: 36rpx 0 0 24rpx;
      .text_3 {
        width: 416rpx;
        height: 26rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
      }
      .text_4 {
        width: 72rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin-top: 2rpx;
      }
    }
    .text-wrapper_2 {
      width: 180rpx;
      height: 30rpx;
      margin: 74rpx 0 0 278rpx;
      .text_5 {
        width: 180rpx;
        height: 30rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 30rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 30rpx;
      }
    }
    .group_3 {
      width: 390rpx;
      height: 40rpx;
      margin: 18rpx 0 0 24rpx;
      .box_1 {
        border-radius: 50%;
        width: 40rpx;
        height: 40rpx;
        border: 1px solid rgba(102, 102, 102, 1);
      }
      .text_6 {
        width: 136rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        margin-top: 2rpx;
      }
    }
    .text-wrapper_3 {
      width: 176rpx;
      height: 24rpx;
      margin-left: 278rpx;
      .text_7 {
        width: 176rpx;
        height: 24rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
      }
    }
    .group_4 {
      width: 178rpx;
      height: 30rpx;
      margin: 14rpx 0 0 278rpx;
      .text-wrapper_4 {
        width: 178rpx;
        height: 30rpx;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
        .text_8 {
          width: 178rpx;
          height: 30rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
        .text_9 {
          width: 178rpx;
          height: 30rpx;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 24rpx;
        }
        .text_10 {
          width: 178rpx;
          height: 30rpx;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 30rpx;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 30rpx;
        }
      }
    }
    .group_5 {
      width: 650rpx;
      height: 72rpx;
      margin: 52rpx 0 0 26rpx;
      .text-group_1 {
        width: 104rpx;
        height: 72rpx;
        .text_11 {
          width: 104rpx;
          height: 26rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 26rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26rpx;
        }
        .text_12 {
          width: 52rpx;
          height: 26rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 26rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26rpx;
          margin: 20rpx 0 0 2rpx;
        }
      }
      .text-group_2 {
        width: 112rpx;
        height: 72rpx;
        .text_13 {
          width: 112rpx;
          height: 26rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 26rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26rpx;
        }
        .text_14 {
          width: 92rpx;
          height: 26rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 26rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26rpx;
          margin: 20rpx 0 0 18rpx;
        }
      }
    }
    .text-wrapper_5 {
      width: 654rpx;
      height: 30rpx;
      margin: 54rpx 0 22rpx 24rpx;
      .text_15 {
        width: 52rpx;
        height: 26rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin-top: 2rpx;
      }
      .text_16 {
        width: 130rpx;
        height: 30rpx;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 30rpx;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 30rpx;
      }
    }
    .text-wrapper_6 {
      position: absolute;
      left: 194rpx;
      top: 134rpx;
      width: 482rpx;
      height: 30rpx;
      .text_17 {
        width: 180rpx;
        height: 30rpx;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 30rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 30rpx;
      }
      .text_18 {
        width: 26rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 28rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 28rpx;
        margin-top: 2rpx;
      }
    }
    .image_2 {
      position: absolute;
      left: 102rpx;
      top: 134rpx;
      width: 156rpx;
      height: 156rpx;
    }
  }
  .block_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    width: 702rpx;
    height: 470rpx;
    margin: 28rpx 0 0 24rpx;
    .text_19 {
      width: 120rpx;
      height: 22rpx;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 30rpx;
      font-family: Source Han Sans CN-Regular;
      font-weight: NaN;
      text-align: left;
      white-space: nowrap;
      line-height: 44rpx;
      margin: 38rpx 0 0 24rpx;
    }
    .group_6 {
      background-color: rgba(245, 245, 245, 1);
      border-radius: 10px;
      height: 356rpx;
      width: 654rpx;
      margin: 32rpx 0 22rpx 24rpx;
      .text-wrapper_7 {
        width: 210rpx;
        height: 22rpx;
        margin: 44rpx 0 0 24rpx;
        .text_20 {
          width: 210rpx;
          height: 22rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 44rpx;
        }
      }
      .text-wrapper_8 {
        width: 80rpx;
        height: 22rpx;
        margin: 240rpx 0 28rpx 544rpx;
        .text_21 {
          width: 80rpx;
          height: 22rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 30rpx;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 44rpx;
        }
      }
    }
  }
  .block_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    height: 352rpx;
    width: 702rpx;
    margin: 14rpx 0 0 24rpx;
    .text-wrapper_9 {
      width: 120rpx;
      height: 22rpx;
      margin: 48rpx 0 0 24rpx;
      .text_22 {
        width: 120rpx;
        height: 22rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 30rpx;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 44rpx;
      }
    }
    .block_5 {
      width: 658rpx;
      height: 238rpx;
      margin: 16rpx 0 28rpx 24rpx;
      .group_7 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        width: 194rpx;
        height: 194rpx;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin-top: 24rpx;
        .image-text_1 {
          width: 92rpx;
          height: 98rpx;
          margin: 48rpx 0 0 52rpx;
          .label_1 {
            width: 62rpx;
            height: 62rpx;
            margin-left: 16rpx;
          }
          .text-group_3 {
            width: 92rpx;
            height: 16rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 22rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 38rpx;
            margin-top: 20rpx;
          }
        }
      }
      .group_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        position: relative;
        width: 194rpx;
        height: 194rpx;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin: 24rpx 0 0 30rpx;
        .image-text_2 {
          width: 92rpx;
          height: 98rpx;
          margin: 48rpx 0 0 52rpx;
          .label_2 {
            width: 62rpx;
            height: 62rpx;
            margin-left: 16rpx;
          }
          .text-group_4 {
            width: 92rpx;
            height: 16rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 22rpx;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 38rpx;
            margin-top: 20rpx;
          }
        }
        .thumbnail_5 {
          position: absolute;
          left: 166rpx;
          top: -14rpx;
          width: 28rpx;
          height: 28rpx;
        }
      }
      .group_9 {
        width: 218rpx;
        height: 238rpx;
        margin-left: 22rpx;
        .text-wrapper_10 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 154rpx;
          width: 218rpx;
          .text_23 {
            width: 84rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 28rpx;
            margin: 64rpx 0 0 68rpx;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 68rpx;
          width: 188rpx;
          margin: 16rpx 0 0 18rpx;
          .text_24 {
            width: 112rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 28rpx;
            margin: 20rpx 0 0 38rpx;
          }
        }
      }
    }
  }
  .block_6 {
    background-color: rgba(255, 255, 255, 1);
    width: 750rpx;
    height: 108rpx;
    justify-content: flex-center;
    margin: 60rpx 0 2rpx 0;
    .group_10 {
      border-radius: 50%;
      width: 34rpx;
      height: 34rpx;
      border: 1px solid rgba(102, 102, 102, 1);
      margin: 38rpx 0 0 24rpx;
    }
    .text_25 {
      width: 52rpx;
      height: 26rpx;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 26rpx;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 26rpx;
      margin: 42rpx 0 0 12rpx;
    }
    .text-wrapper_12 {
      width: 220rpx;
      height: 32rpx;
      overflow-wrap: break-word;
      font-size: 0;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 26rpx;
      margin: 38rpx 0 0 222rpx;
      .text_26 {
        width: 220rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
      }
      .text_27 {
        width: 220rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
      }
      .text_28 {
        width: 220rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 20rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 20rpx;
      }
      .text_29 {
        width: 220rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 32rpx;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 32rpx;
      }
      .text_30 {
        width: 220rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 32rpx;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 32rpx;
      }
    }
    .text-wrapper_13 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 100px;
      height: 64rpx;
      width: 150rpx;
      margin: 22rpx 24rpx 0 12rpx;
      .text_31 {
        width: 104rpx;
        height: 26rpx;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin: 20rpx 0 0 24rpx;
      }
    }
  }
}
