.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 375px;
  height: 874px;
  overflow: hidden;
  .block_1 {
    width: 375px;
    height: 97px;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 32px;
      .text_1 {
        width: 32px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 12px;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 150px;
        margin: 7px 0 0 16px;
      }
      .thumbnail_1 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 251px;
      }
      .thumbnail_2 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 3px;
      }
      .thumbnail_3 {
        width: 19px;
        height: 19px;
        margin: 7px 15px 0 3px;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 54px;
      margin-bottom: 11px;
      .thumbnail_4 {
        width: 9px;
        height: 17px;
        margin: 19px 0 0 18px;
      }
      .text_2 {
        width: 64px;
        height: 22px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 16px;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 22px;
        margin: 16px 0 0 11px;
      }
      .image_1 {
        width: 87px;
        height: 32px;
        margin: 11px 6px 0 180px;
      }
    }
  }
  .block_2 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 261px;
    width: 351px;
    position: relative;
    margin: -1px 0 0 12px;
    .text-wrapper_1 {
      width: 328px;
      height: 13px;
      margin: 18px 0 0 12px;
      .text_3 {
        width: 208px;
        height: 13px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_4 {
        width: 36px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .text-wrapper_2 {
      width: 90px;
      height: 15px;
      margin: 37px 0 0 139px;
      .text_5 {
        width: 90px;
        height: 15px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 15px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
    }
    .group_3 {
      width: 195px;
      height: 20px;
      margin: 9px 0 0 12px;
      .box_1 {
        border-radius: 50%;
        width: 20px;
        height: 20px;
        border: 1px solid rgba(102, 102, 102, 1);
      }
      .text_6 {
        width: 68px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .text-wrapper_3 {
      width: 88px;
      height: 12px;
      margin-left: 139px;
      .text_7 {
        width: 88px;
        height: 12px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 12px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
    }
    .group_4 {
      width: 89px;
      height: 15px;
      margin: 7px 0 0 139px;
      .text-wrapper_4 {
        width: 89px;
        height: 15px;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        .text_8 {
          width: 89px;
          height: 15px;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 12px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
        }
        .text_9 {
          width: 89px;
          height: 15px;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 12px;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
        }
        .text_10 {
          width: 89px;
          height: 15px;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 15px;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
        }
      }
    }
    .group_5 {
      width: 325px;
      height: 36px;
      margin: 26px 0 0 13px;
      .text-group_1 {
        width: 52px;
        height: 36px;
        .text_11 {
          width: 52px;
          height: 13px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 13px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
        }
        .text_12 {
          width: 26px;
          height: 13px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 13px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 10px 0 0 1px;
        }
      }
      .text-group_2 {
        width: 56px;
        height: 36px;
        .text_13 {
          width: 56px;
          height: 13px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 13px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
        }
        .text_14 {
          width: 46px;
          height: 13px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 13px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 100px;
          margin: 10px 0 0 9px;
        }
      }
    }
    .text-wrapper_5 {
      width: 327px;
      height: 15px;
      margin: 27px 0 11px 12px;
      .text_15 {
        width: 26px;
        height: 13px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
      .text_16 {
        width: 65px;
        height: 15px;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 15px;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
    }
    .text-wrapper_6 {
      position: absolute;
      left: 97px;
      top: 67px;
      width: 241px;
      height: 15px;
      .text_17 {
        width: 90px;
        height: 15px;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 15px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_18 {
        width: 13px;
        height: 14px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 14px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin-top: 1px;
      }
    }
    .image_2 {
      position: absolute;
      left: 51px;
      top: 67px;
      width: 78px;
      height: 78px;
    }
  }
  .block_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    width: 351px;
    height: 235px;
    margin: 14px 0 0 12px;
    .text_19 {
      width: 60px;
      height: 11px;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 15px;
      font-family: Source Han Sans CN-Regular;
      font-weight: NaN;
      text-align: left;
      white-space: nowrap;
      line-height: 22px;
      margin: 19px 0 0 12px;
    }
    .group_6 {
      background-color: rgba(245, 245, 245, 1);
      border-radius: 10px;
      height: 178px;
      width: 327px;
      margin: 16px 0 11px 12px;
      .text-wrapper_7 {
        width: 105px;
        height: 11px;
        margin: 22px 0 0 12px;
        .text_20 {
          width: 105px;
          height: 11px;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 15px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 22px;
        }
      }
      .text-wrapper_8 {
        width: 40px;
        height: 11px;
        margin: 120px 0 14px 272px;
        .text_21 {
          width: 40px;
          height: 11px;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 15px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 22px;
        }
      }
    }
  }
  .block_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    height: 176px;
    width: 351px;
    margin: 7px 0 0 12px;
    .text-wrapper_9 {
      width: 60px;
      height: 11px;
      margin: 24px 0 0 12px;
      .text_22 {
        width: 60px;
        height: 11px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 15px;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 22px;
      }
    }
    .block_5 {
      width: 329px;
      height: 119px;
      margin: 8px 0 14px 12px;
      .group_7 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        width: 97px;
        height: 97px;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin-top: 12px;
        .image-text_1 {
          width: 46px;
          height: 49px;
          margin: 24px 0 0 26px;
          .label_1 {
            width: 31px;
            height: 31px;
            margin-left: 8px;
          }
          .text-group_3 {
            width: 46px;
            height: 8px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 11px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 19px;
            margin-top: 10px;
          }
        }
      }
      .group_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        position: relative;
        width: 97px;
        height: 97px;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin: 12px 0 0 15px;
        .image-text_2 {
          width: 46px;
          height: 49px;
          margin: 24px 0 0 26px;
          .label_2 {
            width: 31px;
            height: 31px;
            margin-left: 8px;
          }
          .text-group_4 {
            width: 46px;
            height: 8px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 11px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 19px;
            margin-top: 10px;
          }
        }
        .thumbnail_5 {
          position: absolute;
          left: 83px;
          top: -7px;
          width: 14px;
          height: 14px;
        }
      }
      .group_9 {
        width: 109px;
        height: 119px;
        margin-left: 11px;
        .text-wrapper_10 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 77px;
          width: 109px;
          .text_23 {
            width: 42px;
            height: 14px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 14px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 32px 0 0 34px;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 34px;
          width: 94px;
          margin: 8px 0 0 9px;
          .text_24 {
            width: 56px;
            height: 14px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 14px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 100px;
            margin: 10px 0 0 19px;
          }
        }
      }
    }
  }
  .block_6 {
    background-color: rgba(255, 255, 255, 1);
    width: 375px;
    height: 54px;
    justify-content: flex-center;
    margin: 30px 0 1px 0;
    .group_10 {
      border-radius: 50%;
      width: 17px;
      height: 17px;
      border: 1px solid rgba(102, 102, 102, 1);
      margin: 19px 0 0 12px;
    }
    .text_25 {
      width: 26px;
      height: 13px;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 13px;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 100px;
      margin: 21px 0 0 6px;
    }
    .text-wrapper_12 {
      width: 110px;
      height: 16px;
      overflow-wrap: break-word;
      font-size: 0;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 100px;
      margin: 19px 0 0 111px;
      .text_26 {
        width: 110px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_27 {
        width: 110px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_28 {
        width: 110px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 10px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_29 {
        width: 110px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 16px;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
      .text_30 {
        width: 110px;
        height: 16px;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 16px;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
      }
    }
    .text-wrapper_13 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 100px;
      height: 32px;
      width: 75px;
      margin: 11px 12px 0 6px;
      .text_31 {
        width: 52px;
        height: 13px;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 13px;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 100px;
        margin: 10px 0 0 12px;
      }
    }
  }
}
