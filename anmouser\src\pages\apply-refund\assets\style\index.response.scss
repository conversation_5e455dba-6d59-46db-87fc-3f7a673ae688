.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 100vw;
  height: 233.07vw;
  overflow: hidden;
  .block_1 {
    width: 100vw;
    height: 25.87vw;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 8.54vw;
      .text_1 {
        width: 8.54vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 3.2vw;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 40vw;
        margin: 1.86vw 0 0 4.26vw;
      }
      .thumbnail_1 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 66.93vw;
      }
      .thumbnail_2 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 0.8vw;
      }
      .thumbnail_3 {
        width: 5.07vw;
        height: 5.07vw;
        margin: 1.86vw 4vw 0 0.8vw;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 14.4vw;
      margin-bottom: 2.94vw;
      .thumbnail_4 {
        width: 2.4vw;
        height: 4.54vw;
        margin: 5.06vw 0 0 4.8vw;
      }
      .text_2 {
        width: 17.07vw;
        height: 5.87vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 4.26vw;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 5.87vw;
        margin: 4.26vw 0 0 2.93vw;
      }
      .image_1 {
        width: 23.2vw;
        height: 8.54vw;
        margin: 2.93vw 1.6vw 0 48vw;
      }
    }
  }
  .block_2 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 69.6vw;
    width: 93.6vw;
    position: relative;
    margin: -0.26vw 0 0 3.2vw;
    .text-wrapper_1 {
      width: 87.47vw;
      height: 3.47vw;
      margin: 4.8vw 0 0 3.2vw;
      .text_3 {
        width: 55.47vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_4 {
        width: 9.6vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .text-wrapper_2 {
      width: 24vw;
      height: 4vw;
      margin: 9.86vw 0 0 37.06vw;
      .text_5 {
        width: 24vw;
        height: 4vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 4vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
    }
    .group_3 {
      width: 52vw;
      height: 5.34vw;
      margin: 2.4vw 0 0 3.2vw;
      .box_1 {
        border-radius: 50%;
        width: 5.34vw;
        height: 5.34vw;
        border: 1px solid rgba(102, 102, 102, 1);
      }
      .text_6 {
        width: 18.14vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .text-wrapper_3 {
      width: 23.47vw;
      height: 3.2vw;
      margin-left: 37.07vw;
      .text_7 {
        width: 23.47vw;
        height: 3.2vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.2vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
    }
    .group_4 {
      width: 23.74vw;
      height: 4vw;
      margin: 1.86vw 0 0 37.06vw;
      .text-wrapper_4 {
        width: 23.74vw;
        height: 4vw;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        .text_8 {
          width: 23.74vw;
          height: 4vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_9 {
          width: 23.74vw;
          height: 4vw;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 3.2vw;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_10 {
          width: 23.74vw;
          height: 4vw;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 4vw;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
      }
    }
    .group_5 {
      width: 86.67vw;
      height: 9.6vw;
      margin: 6.93vw 0 0 3.46vw;
      .text-group_1 {
        width: 13.87vw;
        height: 9.6vw;
        .text_11 {
          width: 13.87vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_12 {
          width: 6.94vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 2.66vw 0 0 0.26vw;
        }
      }
      .text-group_2 {
        width: 14.94vw;
        height: 9.6vw;
        .text_13 {
          width: 14.94vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
        }
        .text_14 {
          width: 12.27vw;
          height: 3.47vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 2.66vw 0 0 2.4vw;
        }
      }
    }
    .text-wrapper_5 {
      width: 87.2vw;
      height: 4vw;
      margin: 7.2vw 0 2.93vw 3.2vw;
      .text_15 {
        width: 6.94vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
      .text_16 {
        width: 17.34vw;
        height: 4vw;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 4vw;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
    }
    .text-wrapper_6 {
      position: absolute;
      left: 25.87vw;
      top: 17.87vw;
      width: 64.27vw;
      height: 4vw;
      .text_17 {
        width: 24vw;
        height: 4vw;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 4vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_18 {
        width: 3.47vw;
        height: 3.74vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.73vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin-top: 0.27vw;
      }
    }
    .image_2 {
      position: absolute;
      left: 13.6vw;
      top: 17.87vw;
      width: 20.8vw;
      height: 20.8vw;
    }
  }
  .block_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    width: 93.6vw;
    height: 62.67vw;
    margin: 3.73vw 0 0 3.2vw;
    .text_19 {
      width: 16vw;
      height: 2.94vw;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 4vw;
      font-family: Source Han Sans CN-Regular;
      font-weight: NaN;
      text-align: left;
      white-space: nowrap;
      line-height: 5.87vw;
      margin: 5.06vw 0 0 3.2vw;
    }
    .group_6 {
      background-color: rgba(245, 245, 245, 1);
      border-radius: 10px;
      height: 47.47vw;
      width: 87.2vw;
      margin: 4.26vw 0 2.93vw 3.2vw;
      .text-wrapper_7 {
        width: 28vw;
        height: 2.94vw;
        margin: 5.86vw 0 0 3.2vw;
        .text_20 {
          width: 28vw;
          height: 2.94vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 4vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 5.87vw;
        }
      }
      .text-wrapper_8 {
        width: 10.67vw;
        height: 2.94vw;
        margin: 32vw 0 3.73vw 72.53vw;
        .text_21 {
          width: 10.67vw;
          height: 2.94vw;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 4vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 5.87vw;
        }
      }
    }
  }
  .block_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    height: 46.94vw;
    width: 93.6vw;
    margin: 1.86vw 0 0 3.2vw;
    .text-wrapper_9 {
      width: 16vw;
      height: 2.94vw;
      margin: 6.4vw 0 0 3.2vw;
      .text_22 {
        width: 16vw;
        height: 2.94vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 4vw;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 5.87vw;
      }
    }
    .block_5 {
      width: 87.74vw;
      height: 31.74vw;
      margin: 2.13vw 0 3.73vw 3.2vw;
      .group_7 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        width: 25.87vw;
        height: 25.87vw;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin-top: 3.2vw;
        .image-text_1 {
          width: 12.27vw;
          height: 13.07vw;
          margin: 6.4vw 0 0 6.93vw;
          .label_1 {
            width: 8.27vw;
            height: 8.27vw;
            margin-left: 2.14vw;
          }
          .text-group_3 {
            width: 12.27vw;
            height: 2.14vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 2.93vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 5.07vw;
            margin-top: 2.67vw;
          }
        }
      }
      .group_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        position: relative;
        width: 25.87vw;
        height: 25.87vw;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin: 3.2vw 0 0 4vw;
        .image-text_2 {
          width: 12.27vw;
          height: 13.07vw;
          margin: 6.4vw 0 0 6.93vw;
          .label_2 {
            width: 8.27vw;
            height: 8.27vw;
            margin-left: 2.14vw;
          }
          .text-group_4 {
            width: 12.27vw;
            height: 2.14vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 2.93vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 5.07vw;
            margin-top: 2.67vw;
          }
        }
        .thumbnail_5 {
          position: absolute;
          left: 22.14vw;
          top: -1.86vw;
          width: 3.74vw;
          height: 3.74vw;
        }
      }
      .group_9 {
        width: 29.07vw;
        height: 31.74vw;
        margin-left: 2.94vw;
        .text-wrapper_10 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 20.54vw;
          width: 29.07vw;
          .text_23 {
            width: 11.2vw;
            height: 3.74vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 8.53vw 0 0 9.06vw;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 9.07vw;
          width: 25.07vw;
          margin: 2.13vw 0 0 2.4vw;
          .text_24 {
            width: 14.94vw;
            height: 3.74vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26.67vw;
            margin: 2.66vw 0 0 5.06vw;
          }
        }
      }
    }
  }
  .block_6 {
    background-color: rgba(255, 255, 255, 1);
    width: 100vw;
    height: 14.4vw;
    justify-content: flex-center;
    margin: 8vw 0 0.26vw 0;
    .group_10 {
      border-radius: 50%;
      width: 4.54vw;
      height: 4.54vw;
      border: 1px solid rgba(102, 102, 102, 1);
      margin: 5.06vw 0 0 3.2vw;
    }
    .text_25 {
      width: 6.94vw;
      height: 3.47vw;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 3.46vw;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 26.67vw;
      margin: 5.6vw 0 0 1.6vw;
    }
    .text-wrapper_12 {
      width: 29.34vw;
      height: 4.27vw;
      overflow-wrap: break-word;
      font-size: 0;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 26.67vw;
      margin: 5.06vw 0 0 29.6vw;
      .text_26 {
        width: 29.34vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_27 {
        width: 29.34vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_28 {
        width: 29.34vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 2.66vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_29 {
        width: 29.34vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 4.26vw;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
      .text_30 {
        width: 29.34vw;
        height: 4.27vw;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 4.26vw;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
      }
    }
    .text-wrapper_13 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 100px;
      height: 8.54vw;
      width: 20vw;
      margin: 2.93vw 3.2vw 0 1.6vw;
      .text_31 {
        width: 13.87vw;
        height: 3.47vw;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 3.46vw;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26.67vw;
        margin: 2.66vw 0 0 3.2vw;
      }
    }
  }
}
