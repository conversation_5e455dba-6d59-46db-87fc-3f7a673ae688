.page {
  background-color: rgba(246, 246, 246, 1);
  position: relative;
  width: 10rem;
  height: 23.307rem;
  overflow: hidden;
  .block_1 {
    width: 10rem;
    height: 2.587rem;
    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 0.854rem;
      .text_1 {
        width: 0.854rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 4rem;
        margin: 0.187rem 0 0 0.427rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 6.694rem;
      }
      .thumbnail_2 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 0.08rem;
      }
      .thumbnail_3 {
        width: 0.507rem;
        height: 0.507rem;
        margin: 0.187rem 0.4rem 0 0.08rem;
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 1.44rem;
      margin-bottom: 0.294rem;
      .thumbnail_4 {
        width: 0.24rem;
        height: 0.454rem;
        margin: 0.507rem 0 0 0.48rem;
      }
      .text_2 {
        width: 1.707rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.427rem 0 0 0.294rem;
      }
      .image_1 {
        width: 2.32rem;
        height: 0.854rem;
        margin: 0.294rem 0.16rem 0 4.8rem;
      }
    }
  }
  .block_2 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 10px;
    height: 6.96rem;
    width: 9.36rem;
    position: relative;
    margin: -0.027rem 0 0 0.32rem;
    .text-wrapper_1 {
      width: 8.747rem;
      height: 0.347rem;
      margin: 0.48rem 0 0 0.32rem;
      .text_3 {
        width: 5.547rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_4 {
        width: 0.96rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .text-wrapper_2 {
      width: 2.4rem;
      height: 0.4rem;
      margin: 0.987rem 0 0 3.707rem;
      .text_5 {
        width: 2.4rem;
        height: 0.4rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.4rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
    }
    .group_3 {
      width: 5.2rem;
      height: 0.534rem;
      margin: 0.24rem 0 0 0.32rem;
      .box_1 {
        border-radius: 50%;
        width: 0.534rem;
        height: 0.534rem;
        border: 1px solid rgba(102, 102, 102, 1);
      }
      .text_6 {
        width: 1.814rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .text-wrapper_3 {
      width: 2.347rem;
      height: 0.32rem;
      margin-left: 3.707rem;
      .text_7 {
        width: 2.347rem;
        height: 0.32rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.32rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
    }
    .group_4 {
      width: 2.374rem;
      height: 0.4rem;
      margin: 0.187rem 0 0 3.707rem;
      .text-wrapper_4 {
        width: 2.374rem;
        height: 0.4rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        .text_8 {
          width: 2.374rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_9 {
          width: 2.374rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 0.32rem;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_10 {
          width: 2.374rem;
          height: 0.4rem;
          overflow-wrap: break-word;
          color: rgba(231, 96, 81, 1);
          font-size: 0.4rem;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
      }
    }
    .group_5 {
      width: 8.667rem;
      height: 0.96rem;
      margin: 0.694rem 0 0 0.347rem;
      .text-group_1 {
        width: 1.387rem;
        height: 0.96rem;
        .text_11 {
          width: 1.387rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_12 {
          width: 0.694rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.267rem 0 0 0.027rem;
        }
      }
      .text-group_2 {
        width: 1.494rem;
        height: 0.96rem;
        .text_13 {
          width: 1.494rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
        }
        .text_14 {
          width: 1.227rem;
          height: 0.347rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.267rem 0 0 0.24rem;
        }
      }
    }
    .text-wrapper_5 {
      width: 8.72rem;
      height: 0.4rem;
      margin: 0.72rem 0 0.294rem 0.32rem;
      .text_15 {
        width: 0.694rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
      .text_16 {
        width: 1.734rem;
        height: 0.4rem;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 0.4rem;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
    }
    .text-wrapper_6 {
      position: absolute;
      left: 2.587rem;
      top: 1.787rem;
      width: 6.427rem;
      height: 0.4rem;
      .text_17 {
        width: 2.4rem;
        height: 0.4rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.4rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_18 {
        width: 0.347rem;
        height: 0.374rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.373rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin-top: 0.027rem;
      }
    }
    .image_2 {
      position: absolute;
      left: 1.36rem;
      top: 1.787rem;
      width: 2.08rem;
      height: 2.08rem;
    }
  }
  .block_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    width: 9.36rem;
    height: 6.267rem;
    margin: 0.374rem 0 0 0.32rem;
    .text_19 {
      width: 1.6rem;
      height: 0.294rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 0.4rem;
      font-family: Source Han Sans CN-Regular;
      font-weight: NaN;
      text-align: left;
      white-space: nowrap;
      line-height: 0.587rem;
      margin: 0.507rem 0 0 0.32rem;
    }
    .group_6 {
      background-color: rgba(245, 245, 245, 1);
      border-radius: 10px;
      height: 4.747rem;
      width: 8.72rem;
      margin: 0.427rem 0 0.294rem 0.32rem;
      .text-wrapper_7 {
        width: 2.8rem;
        height: 0.294rem;
        margin: 0.587rem 0 0 0.32rem;
        .text_20 {
          width: 2.8rem;
          height: 0.294rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.4rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.587rem;
        }
      }
      .text-wrapper_8 {
        width: 1.067rem;
        height: 0.294rem;
        margin: 3.2rem 0 0.374rem 7.254rem;
        .text_21 {
          width: 1.067rem;
          height: 0.294rem;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 0.4rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.587rem;
        }
      }
    }
  }
  .block_4 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    height: 4.694rem;
    width: 9.36rem;
    margin: 0.187rem 0 0 0.32rem;
    .text-wrapper_9 {
      width: 1.6rem;
      height: 0.294rem;
      margin: 0.64rem 0 0 0.32rem;
      .text_22 {
        width: 1.6rem;
        height: 0.294rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.4rem;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.587rem;
      }
    }
    .block_5 {
      width: 8.774rem;
      height: 3.174rem;
      margin: 0.214rem 0 0.374rem 0.32rem;
      .group_7 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        width: 2.587rem;
        height: 2.587rem;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin-top: 0.32rem;
        .image-text_1 {
          width: 1.227rem;
          height: 1.307rem;
          margin: 0.64rem 0 0 0.694rem;
          .label_1 {
            width: 0.827rem;
            height: 0.827rem;
            margin-left: 0.214rem;
          }
          .text-group_3 {
            width: 1.227rem;
            height: 0.214rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.293rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.507rem;
            margin-top: 0.267rem;
          }
        }
      }
      .group_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        position: relative;
        width: 2.587rem;
        height: 2.587rem;
        border: 0.8083333373069763px solid rgba(235, 238, 245, 1);
        margin: 0.32rem 0 0 0.4rem;
        .image-text_2 {
          width: 1.227rem;
          height: 1.307rem;
          margin: 0.64rem 0 0 0.694rem;
          .label_2 {
            width: 0.827rem;
            height: 0.827rem;
            margin-left: 0.214rem;
          }
          .text-group_4 {
            width: 1.227rem;
            height: 0.214rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.293rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.507rem;
            margin-top: 0.267rem;
          }
        }
        .thumbnail_5 {
          position: absolute;
          left: 2.214rem;
          top: -0.186rem;
          width: 0.374rem;
          height: 0.374rem;
        }
      }
      .group_9 {
        width: 2.907rem;
        height: 3.174rem;
        margin-left: 0.294rem;
        .text-wrapper_10 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 2.054rem;
          width: 2.907rem;
          .text_23 {
            width: 1.12rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.854rem 0 0 0.907rem;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(0, 0, 0, 0.5);
          border-radius: 5px;
          height: 0.907rem;
          width: 2.507rem;
          margin: 0.214rem 0 0 0.24rem;
          .text_24 {
            width: 1.494rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 2.667rem;
            margin: 0.267rem 0 0 0.507rem;
          }
        }
      }
    }
  }
  .block_6 {
    background-color: rgba(255, 255, 255, 1);
    width: 10rem;
    height: 1.44rem;
    justify-content: flex-center;
    margin: 0.8rem 0 0.027rem 0;
    .group_10 {
      border-radius: 50%;
      width: 0.454rem;
      height: 0.454rem;
      border: 1px solid rgba(102, 102, 102, 1);
      margin: 0.507rem 0 0 0.32rem;
    }
    .text_25 {
      width: 0.694rem;
      height: 0.347rem;
      overflow-wrap: break-word;
      color: rgba(34, 34, 34, 1);
      font-size: 0.346rem;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 2.667rem;
      margin: 0.56rem 0 0 0.16rem;
    }
    .text-wrapper_12 {
      width: 2.934rem;
      height: 0.427rem;
      overflow-wrap: break-word;
      font-size: 0;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 2.667rem;
      margin: 0.507rem 0 0 2.96rem;
      .text_26 {
        width: 2.934rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_27 {
        width: 2.934rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_28 {
        width: 2.934rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.266rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_29 {
        width: 2.934rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(231, 96, 81, 1);
        font-size: 0.426rem;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
      .text_30 {
        width: 2.934rem;
        height: 0.427rem;
        overflow-wrap: break-word;
        color: rgba(11, 206, 148, 1);
        font-size: 0.426rem;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
      }
    }
    .text-wrapper_13 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 100px;
      height: 0.854rem;
      width: 2rem;
      margin: 0.294rem 0.32rem 0 0.16rem;
      .text_31 {
        width: 1.387rem;
        height: 0.347rem;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 0.346rem;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 2.667rem;
        margin: 0.267rem 0 0 0.32rem;
      }
    }
  }
}
